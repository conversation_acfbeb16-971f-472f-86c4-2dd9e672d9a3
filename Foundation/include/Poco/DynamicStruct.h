//
// DynamicStruct.h
//
// Library: Foundation
// Package: Dynamic
// Module:  Struct
//
// Forward header for Struct class to maintain backward compatibility.
//
// Copyright (c) 2007, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#ifndef Foundation_DynamicStruct_INCLUDED
#define Foundation_DynamicStruct_INCLUDED

//@ deprecated
#include "Poco/Dynamic/Struct.h"


#endif // Foundation_DynamicStruct_INCLUDED
