<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8A0B40BF-1AF7-39B7-B8FF-092865984EED}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Foundation</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Foundation.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PocoFoundationd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Foundation.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PocoFoundation</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Foundation.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PocoFoundation</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Foundation.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PocoFoundation</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;UTF8PROC_STATIC;POCO_CMAKE;_DEBUG;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR="Debug";Foundation_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR=\"Debug\";Foundation_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoFoundationd.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoFoundationd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR="Release";Foundation_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR=\"Release\";Foundation_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoFoundation.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoFoundation.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR="MinSizeRel";Foundation_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR=\"MinSizeRel\";Foundation_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoFoundation.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoFoundation.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR="RelWithDebInfo";Foundation_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;POCO_DLL;CMAKE_INTDIR=\"RelWithDebInfo\";Foundation_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>iphlpapi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoFoundation.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoFoundation.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\CMakeFiles\17263cbc493202c384cd976ded6ea157\pocomsg.h.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating pocomsg.h, pocomsg.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Windows Kits\10\bin\10.0.17763.0\x86\mc.exe" -h D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation -r D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation D:/CWorkspace/poco-poco-1.14.2-release/Foundation/src/pocomsg.mc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pocomsg.mc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.h;D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating pocomsg.h, pocomsg.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Windows Kits\10\bin\10.0.17763.0\x86\mc.exe" -h D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation -r D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation D:/CWorkspace/poco-poco-1.14.2-release/Foundation/src/pocomsg.mc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pocomsg.mc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.h;D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Generating pocomsg.h, pocomsg.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files (x86)\Windows Kits\10\bin\10.0.17763.0\x86\mc.exe" -h D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation -r D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation D:/CWorkspace/poco-poco-1.14.2-release/Foundation/src/pocomsg.mc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pocomsg.mc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.h;D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Generating pocomsg.h, pocomsg.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files (x86)\Windows Kits\10\bin\10.0.17763.0\x86\mc.exe" -h D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation -r D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation D:/CWorkspace/poco-poco-1.14.2-release/Foundation/src/pocomsg.mc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pocomsg.mc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.h;D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Foundation/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\cmake\PocoFoundationConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Foundation/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\cmake\PocoFoundationConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Foundation/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\cmake\PocoFoundationConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Foundation/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Foundation/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Foundation\cmake\PocoFoundationConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ASCIIEncoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AbstractObserver.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ActiveDispatcher.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ActiveThreadPool.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ArchiveStrategy.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Ascii.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AsyncChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AsyncNotificationCenter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AtomicCounter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AtomicFlag.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base32Decoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base32Encoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base64Decoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base64Encoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BinaryReader.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BinaryWriter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BufferedBidirectionalStreamBuf.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BufferedStreamBuf.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Bugcheck.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ByteOrder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Channel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Checksum.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Clock.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Condition.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Configurable.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ConsoleChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\CountingStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DataURIStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DataURIStreamFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTime.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTimeFormat.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTimeFormatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTimeParser.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Debugger.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DeflatingStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DigestEngine.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DigestStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIterator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIteratorStrategy.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIterator_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIterator_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryWatcher.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Error.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ErrorHandler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\EventArgs.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\EventChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\EventLogChannel.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Exception.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FIFOBufferStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_C99.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_DEC.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_DUMMY.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_QNX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_SUN.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamRWLock.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamRWLock_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamRWLock_WIN32.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStream_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStream_WIN32.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Format.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Formatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FormattingChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Glob.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Hash.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\HashStatistic.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\HexBinaryDecoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\HexBinaryEncoder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\InflatingStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\JSONFormatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\JSONString.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Latin1Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Latin2Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Latin9Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LineEndingConverter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LocalDateTime.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LogFile.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LogStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Logger.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LoggingFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LoggingRegistry.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MD4Engine.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MD5Engine.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Manifest.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MemoryPool.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MemoryStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Message.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_STD.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent_Android.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex_Android.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NestedDiagnosticContext.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Notification.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NotificationCenter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NotificationQueue.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NullChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NullStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NumberFormatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NumberParser.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NumericString.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PIDFile.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Path.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Path_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Path_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PatternFormatter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Pipe.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl_DUMMY.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PriorityNotificationQueue.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ProcessRunner.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PurgeStrategy.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_Android.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Random.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RandomStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RefCountedObject.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RegularExpression.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RotateStrategy.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Runnable.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SHA1Engine.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SHA2Engine.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_HPUX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_WIN32U.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory_DUMMY.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SignalHandler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SimpleFileChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SortedDirectoryIterator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SplitterChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Stopwatch.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamConverter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamCopier.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamTokenizer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\String.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StringTokenizer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SynchronizedObject.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SyslogChannel.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Task.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TaskManager.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TaskNotification.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TeeStream.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TemporaryFile.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextBufferIterator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextConverter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextEncoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextIterator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ThreadLocal.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ThreadPool.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ThreadTarget.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread_POSIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TimedNotificationQueue.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timespan.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timestamp.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone_UNIX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone_VX.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone_WIN32.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Token.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\URI.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\URIStreamFactory.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\URIStreamOpener.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF16Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF32Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF8Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF8String.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UUID.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UUIDGenerator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UnbufferedStreamBuf.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Unicode.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UnicodeConverter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Var.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\VarHolder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\VarIterator.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\VarVisitor.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Void.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Windows1250Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Windows1251Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Windows1252Encoding.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\WindowsConsoleChannel.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ASCIIEncoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractDelegate.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractEvent.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractObserver.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractPriorityDelegate.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpirationDecorator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpireCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpireLRUCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpireStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveDispatcher.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveMethod.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveResult.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveRunnable.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveStarter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveThreadPool.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Activity.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Alignment.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Any.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ArchiveStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Array.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Ascii.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AsyncChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AsyncNotificationCenter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AsyncObserver.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AtomicCounter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AtomicFlag.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AutoPtr.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AutoReleasePool.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base32Decoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base32Encoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base64Decoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base64Encoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BasicEvent.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BinaryReader.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BinaryWriter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Buffer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BufferAllocator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BufferedBidirectionalStreamBuf.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BufferedStreamBuf.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Bugcheck.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ByteOrder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Channel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Checksum.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ClassLibrary.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ClassLoader.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Clock.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Condition.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Config.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Configurable.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ConsoleChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\CountingStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DataURIStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DataURIStreamFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTime.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTimeFormat.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTimeFormatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTimeParser.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Debugger.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DefaultStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DeflatingStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Delegate.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DigestEngine.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DigestStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIterator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIteratorStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIterator_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIterator_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryWatcher.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\Pair.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\Struct.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\Var.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\VarHolder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\VarIterator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\VarVisitor.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicAny.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicAnyHolder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicStruct.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Error.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ErrorHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\EventArgs.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\EventChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\EventLogChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Exception.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpirationDecorator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Expire.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpireCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpireLRUCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpireStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOBuffer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOBufferStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOEvent.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_C99.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_DEC.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_DUMMY.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_QNX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_SUN.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamRWLock.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamRWLock_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamRWLock_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStream_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStream_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Format.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Formatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FormattingChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Foundation.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FunctionDelegate.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FunctionPriorityDelegate.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Glob.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HMACEngine.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Hash.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashFunction.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashMap.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashSet.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashStatistic.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashTable.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HexBinaryDecoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HexBinaryEncoder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\InflatingStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Instantiator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\JSONFormatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\JSONString.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\KeyValueArgs.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LRUCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LRUStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Latin1Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Latin2Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Latin9Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LineEndingConverter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LinearHashTable.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ListMap.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LocalDateTime.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LogFile.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LogStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Logger.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LoggingFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LoggingRegistry.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MD4Engine.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MD5Engine.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Manifest.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MemoryPool.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MemoryStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Message.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MetaObject.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MetaProgramming.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_STD.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NObserver.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent_Android.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex_Android.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedTuple.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NestedDiagnosticContext.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Notification.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NotificationCenter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NotificationQueue.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NotificationStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NullChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NullStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Nullable.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NumberFormatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NumberParser.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NumericString.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ObjectPool.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Observer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Optional.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\OrderedMap.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\OrderedSet.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PBKDF2Engine.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PIDFile.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Path.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Path_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Path_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PatternFormatter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Pipe.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl_DUMMY.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Poco.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityDelegate.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityEvent.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityExpire.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityNotificationQueue.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ProcessOptions.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ProcessRunner.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PurgeStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_Android.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Random.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RandomStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RecursiveDirectoryIterator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RecursiveDirectoryIteratorImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RefCountedObject.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RegularExpression.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RotateStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Runnable.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RunnableAdapter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SHA1Engine.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SHA2Engine.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ScopedLock.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ScopedUnlock.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_HPUX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_UNIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_WIN32U.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory_DUMMY.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedPtr.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SignalHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SimpleFileChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SimpleHashTable.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SingletonHolder.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SortedDirectoryIterator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SplitterChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Stopwatch.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StrategyCollection.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamConverter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamCopier.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamTokenizer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamUtil.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\String.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StringTokenizer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SynchronizedObject.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SyslogChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Task.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TaskManager.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TaskNotification.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TeeStream.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TemporaryFile.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextBufferIterator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextConverter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextEncoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextIterator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ThreadLocal.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ThreadPool.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ThreadTarget.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread_POSIX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread_VX.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread_WIN32.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TimedNotificationQueue.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timespan.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timestamp.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timezone.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Token.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Tuple.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TypeList.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Types.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\URI.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\URIStreamFactory.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\URIStreamOpener.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF16Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF32Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF8Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF8String.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTFString.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UUID.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UUIDGenerator.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UnWindows.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UnbufferedStreamBuf.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Unicode.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UnicodeConverter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueAccessExpireCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueAccessExpireLRUCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueAccessExpireStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueExpireCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueExpireLRUCache.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueExpireStrategy.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ValidArgs.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Version.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Void.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Windows1250Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Windows1251Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Windows1252Encoding.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\WindowsConsoleChannel.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ordered_hash.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ordered_map.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ordered_set.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\zconf.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\zlib.h" />
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.h" />
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.rc" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pocomsg.mc" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_auto_possess.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_chartables.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_chkdint.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_compile.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_config.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_context.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_convert.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_dfa_match.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_error.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_extuni.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_find_bracket.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_jit_compile.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_maketables.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_match.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_match_data.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_newline.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_ord2utf.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_pattern_info.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_script_run.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_serialize.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_string_utils.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_study.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_substitute.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_substring.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_tables.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_ucd.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_valid_utf.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_xclass.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\adler32.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\compress.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\crc32.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\deflate.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\infback.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\inffast.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\inflate.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\inftrees.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\trees.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\zutil.c" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\utf8proc.c" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\ZERO_CHECK.vcxproj">
      <Project>{9633D67F-878A-3B03-AECD-8F1AE98F930E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>