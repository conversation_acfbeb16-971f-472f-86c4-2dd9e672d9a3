<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ASCIIEncoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AbstractObserver.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ActiveDispatcher.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ActiveThreadPool.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ArchiveStrategy.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Ascii.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AsyncChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AsyncNotificationCenter.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AtomicCounter.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\AtomicFlag.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base32Decoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base32Encoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base64Decoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Base64Encoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BinaryReader.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BinaryWriter.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BufferedBidirectionalStreamBuf.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\BufferedStreamBuf.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Bugcheck.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ByteOrder.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Channel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Checksum.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Clock.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Condition.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Configurable.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ConsoleChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\CountingStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DataURIStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DataURIStreamFactory.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTime.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTimeFormat.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTimeFormatter.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DateTimeParser.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Debugger.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DeflatingStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DigestEngine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DigestStream.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIterator.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIteratorStrategy.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryWatcher.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Error.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ErrorHandler.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\EventArgs.cpp">
      <Filter>Events\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\EventChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\EventLogChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Exception.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FIFOBufferStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamFactory.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamRWLock.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Format.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Formatter.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FormattingChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Glob.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Hash.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\HashStatistic.cpp">
      <Filter>Hashing\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\HexBinaryDecoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\HexBinaryEncoder.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\InflatingStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\JSONFormatter.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\JSONString.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Latin1Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Latin2Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Latin9Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LineEndingConverter.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LocalDateTime.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LogFile.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LogStream.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Logger.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LoggingFactory.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\LoggingRegistry.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MD4Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MD5Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Manifest.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MemoryPool.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\MemoryStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Message.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NestedDiagnosticContext.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Notification.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NotificationCenter.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NotificationQueue.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NullChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NullStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NumberFormatter.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NumberParser.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NumericString.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PIDFile.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Path.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PatternFormatter.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Pipe.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeStream.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PriorityNotificationQueue.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ProcessRunner.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PurgeStrategy.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Random.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RandomStream.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RefCountedObject.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RegularExpression.cpp">
      <Filter>RegExp\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RotateStrategy.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Runnable.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SHA1Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SHA2Engine.cpp">
      <Filter>Crypt\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SignalHandler.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SimpleFileChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SortedDirectoryIterator.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SplitterChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Stopwatch.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamConverter.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamCopier.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StreamTokenizer.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\String.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\StringTokenizer.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SynchronizedObject.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Task.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TaskManager.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TaskNotification.cpp">
      <Filter>Tasks\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TeeStream.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TemporaryFile.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextBufferIterator.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextConverter.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextEncoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TextIterator.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ThreadLocal.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ThreadPool.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\ThreadTarget.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\TimedNotificationQueue.cpp">
      <Filter>Notifications\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timer.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timespan.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timestamp.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Token.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\URI.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\URIStreamFactory.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\URIStreamOpener.cpp">
      <Filter>URI\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF16Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF32Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF8Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UTF8String.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UUID.cpp">
      <Filter>UUID\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UUIDGenerator.cpp">
      <Filter>UUID\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UnbufferedStreamBuf.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Unicode.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\UnicodeConverter.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Var.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\VarHolder.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\VarIterator.cpp">
      <Filter>Dynamic\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\VarVisitor.cpp">
      <Filter>Dynamic\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Void.cpp">
      <Filter>Core\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Windows1250Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Windows1251Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Windows1252Encoding.cpp">
      <Filter>Text\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\WindowsConsoleChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_auto_possess.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_chartables.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_chkdint.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_compile.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_config.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_context.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_convert.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_dfa_match.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_error.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_extuni.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_find_bracket.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_jit_compile.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_maketables.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_match.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_match_data.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_newline.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_ord2utf.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_pattern_info.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_script_run.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_serialize.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_string_utils.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_study.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_substitute.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_substring.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_tables.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_ucd.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_valid_utf.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pcre2_xclass.c">
      <Filter>pcre2\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\adler32.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\compress.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\crc32.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\deflate.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\infback.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\inffast.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\inflate.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\inftrees.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\trees.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\zutil.c">
      <Filter>zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\utf8proc.c">
      <Filter>utf8proc\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIterator_UNIX.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\DirectoryIterator_WIN32U.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment_UNIX.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment_VX.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Environment_WIN32U.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event_VX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Event_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_C99.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_DEC.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_DUMMY.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_QNX.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_SUN.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FPEnvironment_WIN32.cpp">
      <Filter>Core\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamRWLock_POSIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStreamRWLock_WIN32.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStream_POSIX.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\FileStream_WIN32.cpp">
      <Filter>Streams\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File_UNIX.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File_VX.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\File_WIN32U.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_STD.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_VX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Mutex_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent_Android.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent_UNIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedEvent_WIN32U.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex_Android.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex_UNIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\NamedMutex_WIN32U.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Path_UNIX.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Path_WIN32U.cpp">
      <Filter>Filesystem\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl_DUMMY.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl_POSIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\PipeImpl_WIN32.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process_UNIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process_VX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Process_WIN32U.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_Android.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_VX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\RWLock_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore_VX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Semaphore_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_HPUX.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_UNIX.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_VX.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedLibrary_WIN32U.cpp">
      <Filter>SharedLibrary\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory_DUMMY.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory_POSIX.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SharedMemory_WIN32.cpp">
      <Filter>Processes\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\SyslogChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread_POSIX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread_VX.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Thread_WIN32.cpp">
      <Filter>Threading\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone_UNIX.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone_VX.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\Timezone_WIN32.cpp">
      <Filter>DateTime\Source Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ASCIIEncoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractObserver.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractPriorityDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AbstractStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpirationDecorator.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AccessExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveDispatcher.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveMethod.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveResult.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveRunnable.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveStarter.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ActiveThreadPool.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Activity.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Alignment.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Any.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ArchiveStrategy.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Array.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Ascii.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AsyncChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AsyncNotificationCenter.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AsyncObserver.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AtomicCounter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AtomicFlag.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AutoPtr.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\AutoReleasePool.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base32Decoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base32Encoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base64Decoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Base64Encoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BasicEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BinaryReader.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BinaryWriter.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Buffer.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BufferAllocator.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BufferedBidirectionalStreamBuf.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\BufferedStreamBuf.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Bugcheck.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ByteOrder.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Channel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Checksum.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ClassLibrary.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ClassLoader.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Clock.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Condition.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Config.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Configurable.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ConsoleChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\CountingStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DataURIStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DataURIStreamFactory.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTime.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTimeFormat.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTimeFormatter.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DateTimeParser.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Debugger.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DefaultStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DeflatingStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Delegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DigestEngine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DigestStream.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIterator.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIteratorStrategy.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIterator_UNIX.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryIterator_WIN32U.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DirectoryWatcher.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\Pair.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\Struct.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\Var.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\VarHolder.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\VarIterator.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Dynamic\VarVisitor.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicAny.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicAnyHolder.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicFactory.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\DynamicStruct.h">
      <Filter>Dynamic\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment_UNIX.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment_VX.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Environment_WIN32U.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Error.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ErrorHandler.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\EventArgs.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\EventChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\EventLogChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event_VX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Event_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Exception.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpirationDecorator.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Expire.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOBuffer.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOBufferStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FIFOStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_C99.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_DEC.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_DUMMY.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_QNX.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_SUN.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FPEnvironment_WIN32.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamFactory.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamRWLock.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamRWLock_POSIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStreamRWLock_WIN32.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStream_POSIX.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FileStream_WIN32.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File_UNIX.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File_VX.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\File_WIN32U.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Format.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Formatter.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FormattingChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Foundation.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FunctionDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\FunctionPriorityDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Glob.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HMACEngine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Hash.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashFunction.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashMap.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashSet.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashStatistic.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HashTable.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HexBinaryDecoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\HexBinaryEncoder.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\InflatingStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Instantiator.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\JSONFormatter.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\JSONString.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\KeyValueArgs.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LRUStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Latin1Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Latin2Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Latin9Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LineEndingConverter.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LinearHashTable.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ListMap.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LocalDateTime.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LogFile.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LogStream.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Logger.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LoggingFactory.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\LoggingRegistry.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MD4Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MD5Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Manifest.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MemoryPool.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MemoryStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Message.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MetaObject.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\MetaProgramming.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_STD.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_VX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Mutex_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NObserver.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent_Android.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent_UNIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedEvent_WIN32U.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex_Android.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex_UNIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedMutex_WIN32U.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NamedTuple.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NestedDiagnosticContext.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Notification.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NotificationCenter.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NotificationQueue.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NotificationStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NullChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NullStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Nullable.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NumberFormatter.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NumberParser.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\NumericString.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ObjectPool.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Observer.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Optional.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\OrderedMap.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\OrderedSet.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PBKDF2Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PIDFile.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Path.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Path_UNIX.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Path_WIN32U.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PatternFormatter.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Pipe.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl_DUMMY.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl_POSIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeImpl_WIN32.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PipeStream.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform_POSIX.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform_VX.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Platform_WIN32.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Poco.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityDelegate.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityEvent.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityExpire.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityNotificationQueue.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PriorityStrategy.h">
      <Filter>Events\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ProcessOptions.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ProcessRunner.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process_UNIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process_VX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Process_WIN32U.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\PurgeStrategy.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_Android.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_VX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RWLock_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Random.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RandomStream.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RecursiveDirectoryIterator.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RecursiveDirectoryIteratorImpl.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RefCountedObject.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RegularExpression.h">
      <Filter>RegExp\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RotateStrategy.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Runnable.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\RunnableAdapter.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SHA1Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SHA2Engine.h">
      <Filter>Crypt\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ScopedLock.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ScopedUnlock.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore_VX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Semaphore_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_HPUX.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_UNIX.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_VX.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedLibrary_WIN32U.h">
      <Filter>SharedLibrary\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory_DUMMY.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory_POSIX.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedMemory_WIN32.h">
      <Filter>Processes\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SharedPtr.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SignalHandler.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SimpleFileChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SimpleHashTable.h">
      <Filter>Hashing\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SingletonHolder.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SortedDirectoryIterator.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SplitterChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Stopwatch.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StrategyCollection.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamConverter.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamCopier.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamTokenizer.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StreamUtil.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\String.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\StringTokenizer.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SynchronizedObject.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\SyslogChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Task.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TaskManager.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TaskNotification.h">
      <Filter>Tasks\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TeeStream.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TemporaryFile.h">
      <Filter>Filesystem\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextBufferIterator.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextConverter.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextEncoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TextIterator.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ThreadLocal.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ThreadPool.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ThreadTarget.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread_POSIX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread_VX.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Thread_WIN32.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TimedNotificationQueue.h">
      <Filter>Notifications\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timer.h">
      <Filter>Threading\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timespan.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timestamp.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Timezone.h">
      <Filter>DateTime\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Token.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Tuple.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\TypeList.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Types.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\URI.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\URIStreamFactory.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\URIStreamOpener.h">
      <Filter>URI\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF16Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF32Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF8Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTF8String.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UTFString.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UUID.h">
      <Filter>UUID\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UUIDGenerator.h">
      <Filter>UUID\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UnWindows.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UnbufferedStreamBuf.h">
      <Filter>Streams\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Unicode.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UnicodeConverter.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueAccessExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueAccessExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueAccessExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueExpireCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueExpireLRUCache.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\UniqueExpireStrategy.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ValidArgs.h">
      <Filter>Cache\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Version.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Void.h">
      <Filter>Core\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Windows1250Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Windows1251Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\Windows1252Encoding.h">
      <Filter>Text\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\WindowsConsoleChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ordered_hash.h">
      <Filter>Unknown\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ordered_map.h">
      <Filter>Unknown\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\ordered_set.h">
      <Filter>Unknown\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\zconf.h">
      <Filter>Unknown\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include\Poco\zlib.h">
      <Filter>Unknown\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\src\pocomsg.mc">
      <Filter>Logging\Message Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\CMakeFiles\17263cbc493202c384cd976ded6ea157\pocomsg.h.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Foundation\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc">
      <Filter>Resources</Filter>
    </ResourceCompile>
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\pocomsg.rc">
      <Filter>Logging\Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{2C516C06-2879-3BC4-A651-E2976B73484C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache">
      <UniqueIdentifier>{8B2B69F9-8F3A-35A0-AE60-FA3925CAF89B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache\Header Files">
      <UniqueIdentifier>{E6F92DBB-C6C8-3816-99F8-46DFA3FE7961}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core">
      <UniqueIdentifier>{1BF66DF9-0017-3967-9A1E-F7154224F6A7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Header Files">
      <UniqueIdentifier>{22371C22-4F95-3528-A3C6-E3E446E4A96C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Source Files">
      <UniqueIdentifier>{CD4CEDC9-91A0-3928-A847-88338E455D3F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt">
      <UniqueIdentifier>{60062548-2953-3F54-85E1-44809D6CCAE8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt\Header Files">
      <UniqueIdentifier>{6E67BAFF-8F3F-30B1-AC72-7E3F94A7B07E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypt\Source Files">
      <UniqueIdentifier>{6B08E0D8-A1B8-37AB-93E6-0883F5F006F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime">
      <UniqueIdentifier>{E277410E-C6D8-3816-88FB-CA3CAAD0C9A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime\Header Files">
      <UniqueIdentifier>{C5AE85CA-EB5D-3881-9C7A-8CC8F2DF5764}</UniqueIdentifier>
    </Filter>
    <Filter Include="DateTime\Source Files">
      <UniqueIdentifier>{BD4044E0-492E-3D0E-AFE9-C9BC81598E5C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic">
      <UniqueIdentifier>{0A5A6FCE-2183-3CFC-B9B7-C5B628E39987}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic\Header Files">
      <UniqueIdentifier>{1ED77B52-A37F-3C0F-8CDD-0149946EF58C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic\Source Files">
      <UniqueIdentifier>{5BCE3A12-5FF4-345D-9639-16743AD78FCA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Events">
      <UniqueIdentifier>{8804C94A-663C-3A49-82FD-042180B52240}</UniqueIdentifier>
    </Filter>
    <Filter Include="Events\Header Files">
      <UniqueIdentifier>{C388613D-CFB7-3200-8541-006DA13668AD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Events\Source Files">
      <UniqueIdentifier>{1F1418A3-20EB-359A-8C6B-6F8086B11548}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem">
      <UniqueIdentifier>{34F8F4F7-58A6-3E4B-9B6A-E08066700CA6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem\Header Files">
      <UniqueIdentifier>{0A6F3397-B97A-3A38-AA8B-B91E9651BED6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Filesystem\Source Files">
      <UniqueIdentifier>{4BB93A3A-8FEC-347D-A6AC-131422439A83}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing">
      <UniqueIdentifier>{DAF7601B-D4E8-3C78-807A-D33FDA32FC49}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing\Header Files">
      <UniqueIdentifier>{F4DC22C0-4C8B-393B-A3FB-AD5A7E5C56BD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hashing\Source Files">
      <UniqueIdentifier>{5ECF6B27-6E5B-3600-B229-05342A921A4B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{77DD1265-CEA9-3E20-B0B8-41A75CB4798F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Header Files">
      <UniqueIdentifier>{9EA166EB-4430-311B-9B67-C0F6954BE80C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Message Files">
      <UniqueIdentifier>{02438070-23BF-363B-BD32-AF27C53F58E5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Resource Files">
      <UniqueIdentifier>{274CF07A-D4F9-3C47-9065-8E58E728A75E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Source Files">
      <UniqueIdentifier>{4A55DBB4-307C-33A1-A81B-69ECF009D5EF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications">
      <UniqueIdentifier>{47AF6FFC-414A-341E-9244-E495E26D2E2C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications\Header Files">
      <UniqueIdentifier>{A201CAB9-50F6-3B1E-860E-D30D5E27DFEF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications\Source Files">
      <UniqueIdentifier>{1855B928-5F5E-3AFB-AD82-217DD710E2AA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes">
      <UniqueIdentifier>{D15FCCDF-13D8-3E03-B64F-ECBA3B545F53}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes\Header Files">
      <UniqueIdentifier>{C31A7068-94D5-3663-996E-207B82697C9F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Processes\Source Files">
      <UniqueIdentifier>{D10C4036-F32A-3CF6-8278-B3BA99A53DF2}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegExp">
      <UniqueIdentifier>{7DADA23E-9B50-3A5D-998C-BFF9A1FEE9F8}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegExp\Header Files">
      <UniqueIdentifier>{C00CE8A6-8D74-3190-A6B8-2E80EC79A5A5}</UniqueIdentifier>
    </Filter>
    <Filter Include="RegExp\Source Files">
      <UniqueIdentifier>{A727486C-442C-3B7C-9069-9832C0B35333}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resources">
      <UniqueIdentifier>{4EE0C587-0023-37BD-9239-61AA155C92EE}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary">
      <UniqueIdentifier>{7EF8FA79-EA2C-3249-975E-F7D16467C7D5}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary\Header Files">
      <UniqueIdentifier>{39F2D94E-6C24-3352-A98C-92C9AA98358A}</UniqueIdentifier>
    </Filter>
    <Filter Include="SharedLibrary\Source Files">
      <UniqueIdentifier>{2C1CE5E4-0879-3C99-BB8C-49DC930E6B56}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams">
      <UniqueIdentifier>{CC842E75-1214-3DC8-BB44-1E6688D02281}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams\Header Files">
      <UniqueIdentifier>{AD0491C0-C31A-3355-8245-0F066F25A88B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Streams\Source Files">
      <UniqueIdentifier>{ECA5C0F0-A712-39B8-8DE8-9755F2D32324}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks">
      <UniqueIdentifier>{0EFFF0B1-81FD-319E-B188-995FFF1F47DA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks\Header Files">
      <UniqueIdentifier>{1FC895AF-3F54-3AB4-956D-E3430637ADF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tasks\Source Files">
      <UniqueIdentifier>{C09EC534-1CB7-337B-A7B6-83706F41C133}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text">
      <UniqueIdentifier>{C42A3875-E4D7-3F40-BD6B-90831E33A40F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Header Files">
      <UniqueIdentifier>{DFFA89FA-0799-3054-B33C-4B54F0A598F2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Text\Source Files">
      <UniqueIdentifier>{28F17ED9-8191-3B92-891A-D69A499B3B06}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading">
      <UniqueIdentifier>{6B0DB4BB-C638-3F18-8BF9-A6CCE6A7578A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading\Header Files">
      <UniqueIdentifier>{AF790EB8-62F4-3A63-B080-5F3FBEF694BD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading\Source Files">
      <UniqueIdentifier>{8C282616-6B72-3053-ABB9-F9F45388ACC8}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI">
      <UniqueIdentifier>{70DBA030-9270-3618-B7A5-BABCFC9A5393}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI\Header Files">
      <UniqueIdentifier>{917F66CD-C7F6-3178-B207-A305625C2D87}</UniqueIdentifier>
    </Filter>
    <Filter Include="URI\Source Files">
      <UniqueIdentifier>{0119C438-8C83-3911-B80D-7265F0FC942F}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID">
      <UniqueIdentifier>{9A7BEB7D-E7D4-31E2-AD83-1953C67CE902}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID\Header Files">
      <UniqueIdentifier>{E5494EC6-AFB2-3A08-981A-6C51EBA29A24}</UniqueIdentifier>
    </Filter>
    <Filter Include="UUID\Source Files">
      <UniqueIdentifier>{F1B00B35-9FD0-3B3E-A03C-19E4F9D37053}</UniqueIdentifier>
    </Filter>
    <Filter Include="Unknown">
      <UniqueIdentifier>{A3772199-5A72-38D5-A88C-5EED7583D5D7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Unknown\Header Files">
      <UniqueIdentifier>{F9D73EF9-1C0C-3FF7-A06E-EB185048CA59}</UniqueIdentifier>
    </Filter>
    <Filter Include="pcre2">
      <UniqueIdentifier>{4D112804-5452-30EC-BEC1-CCA1201DCEFE}</UniqueIdentifier>
    </Filter>
    <Filter Include="pcre2\Source Files">
      <UniqueIdentifier>{C705FA75-F5FE-366C-9C94-41C1E1E1A8AB}</UniqueIdentifier>
    </Filter>
    <Filter Include="utf8proc">
      <UniqueIdentifier>{90BAB186-3B86-3342-8DEB-0E3B6C7457D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="utf8proc\Source Files">
      <UniqueIdentifier>{250CE6DE-438C-3F2A-934A-948DD4D64369}</UniqueIdentifier>
    </Filter>
    <Filter Include="zlib">
      <UniqueIdentifier>{CD63FD95-0AEF-3280-8998-C797C2E757C4}</UniqueIdentifier>
    </Filter>
    <Filter Include="zlib\Source Files">
      <UniqueIdentifier>{3B3F4936-CAC4-3554-B96C-E0DA5E670DEC}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
