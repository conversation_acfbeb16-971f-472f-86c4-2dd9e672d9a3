<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\AbstractHTTPRequestHandler.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DNS.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DatagramSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DatagramSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\DialogSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\EscapeHTMLStream.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\FTPClientSession.cpp">
      <Filter>FTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\FTPStreamFactory.cpp">
      <Filter>FTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\FilePartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTMLForm.cpp">
      <Filter>HTML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPAuthenticationParams.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPBasicCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPBufferAllocator.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPChunkedStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPClientSession.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPCookie.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPDigestCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPFixedLengthStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPHeaderStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPIOStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPMessage.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPNTLMCredentials.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPRequest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPRequestHandler.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPRequestHandlerFactory.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPResponse.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServer.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerConnection.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerConnectionFactory.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerParams.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerRequest.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerRequestImpl.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerResponse.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerResponseImpl.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPServerSession.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPSession.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPSessionFactory.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPSessionInstantiator.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPStream.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HTTPStreamFactory.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\HostEntry.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPClient.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPEventArgs.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPPacket.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPPacketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPSocket.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPSocketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ICMPv4PacketImpl.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\IPAddress.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\IPAddressImpl.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MailMessage.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MailRecipient.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MailStream.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MediaType.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MessageHeader.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MulticastSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MultipartReader.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\MultipartWriter.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTLMCredentials.cpp">
      <Filter>NTLM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTPClient.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTPEventArgs.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NTPPacket.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NameValueCollection.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\Net.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NetException.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NetworkInterface.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\NullPartHandler.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\OAuth10Credentials.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\OAuth20Credentials.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\POP3ClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PartHandler.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PartStore.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\PollSet.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\QuotedPrintableDecoder.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\QuotedPrintableEncoder.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RawSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RawSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RemoteSyslogChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\RemoteSyslogListener.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SMTPChannel.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SMTPClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SSPINTLMCredentials.cpp">
      <Filter>NTLM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ServerSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\ServerSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\Socket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketAddress.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketAddressImpl.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketNotification.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketNotifier.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketProactor.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketReactor.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\SocketStream.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\StreamSocket.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\StreamSocketImpl.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\StringPartSource.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServer.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerConnection.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerConnectionFactory.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerDispatcher.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\TCPServerParams.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\UDPClient.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\UDPServerParams.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\WebSocket.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\WebSocketImpl.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\wepoll.c">
      <Filter>Unknown\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\AbstractHTTPRequestHandler.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DNS.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DatagramSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DatagramSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\DialogSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\EscapeHTMLStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\FTPClientSession.h">
      <Filter>FTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\FTPStreamFactory.h">
      <Filter>FTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\FilePartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTMLForm.h">
      <Filter>HTML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPAuthenticationParams.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPBasicCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPBasicStreamBuf.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPBufferAllocator.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPChunkedStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPClientSession.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPCookie.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPDigestCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPFixedLengthStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPHeaderStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPIOStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPMessage.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPNTLMCredentials.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPRequest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPRequestHandler.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPRequestHandlerFactory.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPResponse.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServer.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerConnection.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerConnectionFactory.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerParams.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerRequest.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerRequestImpl.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerResponse.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerResponseImpl.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPServerSession.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPSession.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPSessionFactory.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPSessionInstantiator.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPStream.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HTTPStreamFactory.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\HostEntry.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPClient.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPEventArgs.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPPacket.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPPacketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPSocket.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPSocketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ICMPv4PacketImpl.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\IPAddress.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\IPAddressImpl.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MailMessage.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MailRecipient.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MailStream.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MediaType.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MessageHeader.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MultiSocketPoller.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MulticastSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MultipartReader.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\MultipartWriter.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTLMCredentials.h">
      <Filter>NTLM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTPClient.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTPEventArgs.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NTPPacket.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NameValueCollection.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\Net.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NetException.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NetworkInterface.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\NullPartHandler.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\OAuth10Credentials.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\OAuth20Credentials.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\POP3ClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ParallelSocketAcceptor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ParallelSocketReactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PartHandler.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PartStore.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\PollSet.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\QuotedPrintableDecoder.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\QuotedPrintableEncoder.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RawSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RawSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RemoteSyslogChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\RemoteSyslogListener.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SMTPChannel.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SMTPClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SSPINTLMCredentials.h">
      <Filter>NTLM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ServerSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\ServerSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SingleSocketPoller.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\Socket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketAcceptor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketAddress.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketAddressImpl.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketConnector.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketDefs.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketNotification.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketNotifier.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketProactor.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketReactor.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\SocketStream.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\StreamSocket.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\StreamSocketImpl.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\StringPartSource.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServer.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerConnection.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerConnectionFactory.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerDispatcher.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\TCPServerParams.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPClient.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPHandler.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPServer.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPServerParams.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\UDPSocketReader.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\WebSocket.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\include\Poco\Net\WebSocketImpl.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\src\wepoll.h">
      <Filter>Unknown\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Net\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc">
      <Filter>Resources</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="FTP">
      <UniqueIdentifier>{14329BF2-ACBE-396E-908A-CBFC73AE3B9E}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTP\Header Files">
      <UniqueIdentifier>{8C34416F-3F3C-3597-A631-101793C1ABC2}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTP\Source Files">
      <UniqueIdentifier>{8AC4CEF0-0518-3DEF-B894-DDB0836E6C89}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML">
      <UniqueIdentifier>{19FDF6C4-33D4-3DCD-994B-A95B15C35A31}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Header Files">
      <UniqueIdentifier>{8EDF7A52-C352-39C8-AF81-F7C9C989C6CE}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Source Files">
      <UniqueIdentifier>{5611874F-DC59-3CB2-AD0A-D504D8BC1227}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP">
      <UniqueIdentifier>{64729F98-C6AC-34B6-AD9A-B04716DBE48E}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient">
      <UniqueIdentifier>{C713DDF2-C9E6-3EEB-86A7-1BD951D1D1D1}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Header Files">
      <UniqueIdentifier>{E4483A9D-E5E7-3554-8AB5-86F42B6E091E}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Source Files">
      <UniqueIdentifier>{14D6682C-AC8A-3644-981B-4BF3CAFC6B92}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer">
      <UniqueIdentifier>{1F998836-EC1F-3046-8003-61B87BE2E986}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Header Files">
      <UniqueIdentifier>{B62650F6-7CEC-34FA-A1D7-96A2FC783DFC}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Source Files">
      <UniqueIdentifier>{FC98B992-D3F3-30B2-94B2-F45C6E262603}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Header Files">
      <UniqueIdentifier>{34E1BC8E-28F1-3063-9665-D23311AF65F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Source Files">
      <UniqueIdentifier>{B9A59C2C-E306-314D-A71F-98ADB444F1F1}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP">
      <UniqueIdentifier>{B33FAAFB-E050-36FF-8651-B6609B7A1733}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Header Files">
      <UniqueIdentifier>{B8664246-98ED-32B0-9D47-6DECA6B80949}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Source Files">
      <UniqueIdentifier>{63D50062-AFD6-3B64-95C0-88243278421C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{77DD1265-CEA9-3E20-B0B8-41A75CB4798F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Header Files">
      <UniqueIdentifier>{9EA166EB-4430-311B-9B67-C0F6954BE80C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Source Files">
      <UniqueIdentifier>{4A55DBB4-307C-33A1-A81B-69ECF009D5EF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{6B6D4B1F-C04B-370F-AEAA-C39DB393C2EA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{DF7E4DB7-8CD4-35DA-8549-BEA27A2CDD1F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{8EBB4FB9-2570-384D-A11C-46388DBB46F6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages">
      <UniqueIdentifier>{08938BDF-AEB7-3E0A-9772-10CC0914FE24}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Header Files">
      <UniqueIdentifier>{7D3E552F-6463-302E-8821-0BD152638EB2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Source Files">
      <UniqueIdentifier>{413B9A97-8E9C-30EC-BA5E-B9462CEE4527}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM">
      <UniqueIdentifier>{E67D7243-0E9E-32C8-B835-44776A03B5D1}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM\Header Files">
      <UniqueIdentifier>{1AE2AF67-251E-34AD-BFA6-34031E2269C8}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTLM\Source Files">
      <UniqueIdentifier>{DC693509-2290-3963-B95E-DEE1482E836B}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP">
      <UniqueIdentifier>{267D2530-CE13-3D9E-A8E6-80BFDB06DE33}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Header Files">
      <UniqueIdentifier>{F50D8CB4-1FA8-35A6-9FAB-F5065A0E5059}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Source Files">
      <UniqueIdentifier>{CDE96ECF-5BE1-349B-B809-0B14E7C315F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore">
      <UniqueIdentifier>{5AABFD66-7C4E-3F4D-A602-DCBAE95550E5}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Header Files">
      <UniqueIdentifier>{FB921E76-5011-3718-AAB2-46C4C7FC6F52}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Source Files">
      <UniqueIdentifier>{DAD87D08-16E9-3104-95DF-E43C2457CF00}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth">
      <UniqueIdentifier>{FB9D0E76-69CE-31BC-A53D-C36C733ECA88}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Header Files">
      <UniqueIdentifier>{AE6265B6-3250-3F1A-8F8C-EFCC03E9908C}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Source Files">
      <UniqueIdentifier>{E6D58DEF-57CD-32FF-A2DB-7AE2DB59940E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor">
      <UniqueIdentifier>{991DF02F-5479-3153-B344-3C4805F4F59C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Header Files">
      <UniqueIdentifier>{C39CFC21-91FB-39E3-8CEB-D7BC368DA5A8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Source Files">
      <UniqueIdentifier>{C0810D25-8B3D-361D-A823-1A9A87E3559E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resources">
      <UniqueIdentifier>{4EE0C587-0023-37BD-9239-61AA155C92EE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets">
      <UniqueIdentifier>{BE183AEF-A4E8-3B6A-947B-38EC2F9B47D6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Header Files">
      <UniqueIdentifier>{646692E0-AE87-3814-B59C-1FC03B5BB9AD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Source Files">
      <UniqueIdentifier>{C60C79D6-213F-3EFC-ABD4-576B9DA3262C}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{FFFE3F39-B4FB-3FB4-95D4-DE927783DC04}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{3042B0D6-0AEF-3252-8A1B-015628C0FF22}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{E3A7DA36-9BB0-3CE8-AC08-F31BF5A74DC8}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP">
      <UniqueIdentifier>{2887D2E7-2242-3F1A-A374-37662544EF90}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Header Files">
      <UniqueIdentifier>{9AA172F1-941E-3B3D-A36A-5EB1F7EE29EC}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Source Files">
      <UniqueIdentifier>{E6883110-63B9-3DC3-8643-9D06AB43EB91}</UniqueIdentifier>
    </Filter>
    <Filter Include="Unknown">
      <UniqueIdentifier>{A3772199-5A72-38D5-A88C-5EED7583D5D7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Unknown\Header Files">
      <UniqueIdentifier>{F9D73EF9-1C0C-3FF7-A06E-EB185048CA59}</UniqueIdentifier>
    </Filter>
    <Filter Include="Unknown\Source Files">
      <UniqueIdentifier>{810845F8-F395-3ABF-982A-92273EFE7548}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{D1A8F050-E073-38BE-BD00-7516F720BEF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{2A1EC3EE-B36C-355C-A1F7-134DF1AB7828}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{36BF5E5C-4694-3143-B064-FB906A8CE922}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
