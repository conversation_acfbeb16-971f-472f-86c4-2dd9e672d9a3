<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DA04B7FC-4BEE-37BC-94C6-805667C90279}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Prometheus</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Prometheus.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PocoPrometheusd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Prometheus.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PocoPrometheus</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Prometheus.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PocoPrometheus</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Prometheus.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PocoPrometheus</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;_DEBUG;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";Prometheus_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";Prometheus_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNetd.lib;..\lib\PocoFoundationd.lib;iphlpapi.lib;iphlpapi.lib;ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoPrometheusd.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoPrometheusd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="Release";Prometheus_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";Prometheus_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNet.lib;..\lib\PocoFoundation.lib;iphlpapi.lib;iphlpapi.lib;ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoPrometheus.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoPrometheus.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="MinSizeRel";Prometheus_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"MinSizeRel\";Prometheus_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNet.lib;..\lib\PocoFoundation.lib;iphlpapi.lib;iphlpapi.lib;ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoPrometheus.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoPrometheus.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR="RelWithDebInfo";Prometheus_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;POCO_DLL;UTF8PROC_STATIC;POCO_CMAKE;POCO_ENABLE_CPP14;POCO_ENABLE_CPP11;POCO_OS_FAMILY_WINDOWS;UNICODE;_UNICODE;CMAKE_INTDIR=\"RelWithDebInfo\";Prometheus_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include;D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src;D:\CWorkspace\poco-poco-1.14.2-release\Net\include;D:\CWorkspace\poco-poco-1.14.2-release\Foundation\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\lib\PocoNet.lib;..\lib\PocoFoundation.lib;iphlpapi.lib;iphlpapi.lib;ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/lib/PocoPrometheus.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/bin/PocoPrometheus.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Prometheus/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Prometheus/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\cmake\PocoPrometheusConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Prometheus\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Prometheus/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Prometheus/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\cmake\PocoPrometheusConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Prometheus\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Prometheus/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Prometheus/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\cmake\PocoPrometheusConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Prometheus\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-release/Prometheus/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-release -BD:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-release/cmake-debug-build/Prometheus/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\cmake\PocoPrometheusConfig.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakePackageConfigHelpers.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Prometheus\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Collector.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Counter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Gauge.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Histogram.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\IntCounter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\IntGauge.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\LabeledMetric.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\MetricsRequestHandler.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\MetricsServer.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\ProcessCollector.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\Registry.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\TextExporter.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\src\ThreadPoolCollector.cpp" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\AtomicFloat.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\CallbackMetric.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Collector.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Counter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Exporter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Gauge.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Histogram.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\IntCounter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\IntGauge.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\LabeledMetric.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\LabeledMetricImpl.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Metric.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\MetricsRequestHandler.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\MetricsServer.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\ProcessCollector.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Prometheus.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\Registry.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\TextExporter.h" />
    <ClInclude Include="D:\CWorkspace\poco-poco-1.14.2-release\Prometheus\include\Poco\Prometheus\ThreadPoolCollector.h" />
    <ResourceCompile Include="D:\CWorkspace\poco-poco-1.14.2-release\DLLVersion.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\ZERO_CHECK.vcxproj">
      <Project>{9633D67F-878A-3B03-AECD-8F1AE98F930E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Foundation\Foundation.vcxproj">
      <Project>{8A0B40BF-1AF7-39B7-B8FF-092865984EED}</Project>
      <Name>Foundation</Name>
    </ProjectReference>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-release\cmake-debug-build\Net\Net.vcxproj">
      <Project>{EE620DD5-5342-3425-AF5D-05AEF6F2230A}</Project>
      <Name>Net</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>