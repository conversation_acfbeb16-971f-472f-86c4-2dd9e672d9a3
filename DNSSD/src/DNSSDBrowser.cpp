//
// DNSServiceBrowser.cpp
//
// $Id: //poco/1.7/DNSSD/src/DNSSDBrowser.cpp#1 $
//
// Library: DNSSD
// Package: Core
// Module:  DNSServiceBrowser
//
// Copyright (c) 2006-2024, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#include "Poco/DNSSD/DNSSDBrowser.h"


namespace Poco {
namespace DNSSD {


DNSSDBrowser::DNSSDBrowser()
{
}


DNSSDBrowser::~DNSSDBrowser()
{
}


} } // namespace Poco::DNSSD
