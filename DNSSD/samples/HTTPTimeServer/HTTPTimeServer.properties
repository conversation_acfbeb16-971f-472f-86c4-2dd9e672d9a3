# This is a sample configuration file for HTTPTimeServer

logging.loggers.root.channel.class = ConsoleChannel
logging.loggers.app.name = Application
logging.loggers.app.channel = c1
logging.formatters.f1.class = PatternFormatter
logging.formatters.f1.pattern = [%p] %t
logging.channels.c1.class = ConsoleChannel
logging.channels.c1.formatter = f1
HTTPTimeServer.format = %W, %e %b %y %H:%M:%S %Z
HTTPTimeServer.port   = 9980
#HTTPTimeServer.maxQueued  = 200
#HTTPTimeServer.maxThreads = 64
