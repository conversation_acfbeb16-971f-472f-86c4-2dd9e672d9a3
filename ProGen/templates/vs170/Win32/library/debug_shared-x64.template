<Configuration
	Name="debug_shared|x64"
	OutputDirectory="${project.outdir}\bin64\"
	IntermediateDirectory="obj64\${project.name}\$(ConfigurationName)"
	ConfigurationType="2"
	CharacterSet="2"
	>
	<Tool
		Name="VCPreBuildEventTool"
	/>
	<Tool
		Name="VCCustomBuildTool"
	/>
	<Tool
		Name="VCXMLDataGeneratorTool"
	/>
	<Tool
		Name="VCWebServiceProxyGeneratorTool"
	/>
	<Tool
		Name="VCMIDLTool"
	/>
	<Tool
		Name="VCCLCompilerTool"
		Optimization="0"
		AdditionalIncludeDirectories=".\include;${configuration.compiler.includes}"
		PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;${configuration.compiler.defines}"
		StringPooling="true"
		BasicRuntimeChecks="3"
		RuntimeLibrary="3"
		BufferSecurityCheck="true"
		TreatWChar_tAsBuiltInType="true"
		ForceConformanceInForLoopScope="true"
		RuntimeTypeInfo="true"
		UsePrecompiledHeader="0"
		WarningLevel="3"
		Detect64BitPortabilityProblems="false"
		DebugInformationFormat="3"
		CompileAs="0"
		ProgramDatabaseFileName="$(OutDir)$(TargetName).pdb"
		LanguageStandard="${vc.project.compiler.std.cpp}"
		LanguageStandard_C="${vc.project.compiler.std.c}"
		DisableSpecificWarnings="${configuration.compiler.disableWarnings}"
		AdditionalOptions="${configuration.compiler.additionalOptions}"
	/>
	<Tool
		Name="VCManagedResourceCompilerTool"
	/>
	<Tool
		Name="VCResourceCompilerTool"
	/>
	<Tool
		Name="VCPreLinkEventTool"
	/>
	<Tool
		Name="VCLinkerTool"
		AdditionalDependencies="${configuration.linker.dependencies}"
		OutputFile="${project.outdir}\bin64\${project.target}64d.dll"
		LinkIncremental="2"
		SuppressStartupBanner="true"
		GenerateDebugInformation="true"
		ProgramDatabaseFile="$(OutDir)$(TargetName).pdb"
		AdditionalLibraryDirectories="${project.pocobase}\lib64"
		SubSystem="1"
		ImportLibrary="${project.outdir}\lib64\${project.target}d.lib"
		TargetMachine="17"
		AdditionalOptions="${configuration.linker.additionalOptions}"
	/>
	<Tool
		Name="VCALinkTool"
	/>
	<Tool
		Name="VCManifestTool"
	/>
	<Tool
		Name="VCXDCMakeTool"
	/>
	<Tool
		Name="VCBscMakeTool"
	/>
	<Tool
		Name="VCFxCopTool"
	/>
	<Tool
		Name="VCAppVerifierTool"
	/>
	<Tool
		Name="VCPostBuildEventTool"
	/>
</Configuration>
