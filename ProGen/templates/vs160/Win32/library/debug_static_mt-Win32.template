<Configuration
	Name="debug_static_mt|Win32"
	OutputDirectory="${project.outdir}\lib\"
	IntermediateDirectory="obj\${project.name}\$(ConfigurationName)"
	ConfigurationType="4"
	CharacterSet="2"
	>
	<Tool
		Name="VCPreBuildEventTool"
	/>
	<Tool
		Name="VCCustomBuildTool"
	/>
	<Tool
		Name="VCXMLDataGeneratorTool"
	/>
	<Tool
		Name="VCWebServiceProxyGeneratorTool"
	/>
	<Tool
		Name="VCMIDLTool"
	/>
	<Tool
		Name="VCCLCompilerTool"
		Optimization="0"
		AdditionalIncludeDirectories=".\include;${configuration.compiler.includes}"
		PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;${configuration.compiler.defines}"
		StringPooling="true"
		BasicRuntimeChecks="3"
		RuntimeLibrary="1"
		BufferSecurityCheck="true"
		TreatWChar_tAsBuiltInType="true"
		ForceConformanceInForLoopScope="true"
		RuntimeTypeInfo="true"
		UsePrecompiledHeader="0"
		ProgramDataBaseFileName="${project.outdir}\lib\${project.target}mtd.pdb"
		WarningLevel="3"
		Detect64BitPortabilityProblems="false"
		DebugInformationFormat="3"
		CompileAs="0"
		ProgramDatabaseFileName="$(OutDir)$(TargetName).pdb"
		LanguageStandard="${vc.project.compiler.std.cpp}"
		LanguageStandard_C="${vc.project.compiler.std.c}"
		DisableSpecificWarnings="${configuration.compiler.disableWarnings}"
		AdditionalOptions="${configuration.compiler.additionalOptions}"
	/>
	<Tool
		Name="VCManagedResourceCompilerTool"
	/>
	<Tool
		Name="VCResourceCompilerTool"
	/>
	<Tool
		Name="VCPreLinkEventTool"
	/>
	<Tool
		Name="VCLibrarianTool"
		OutputFile="${project.outdir}\lib\${project.target}mtd.lib"
	/>
	<Tool
		Name="VCALinkTool"
	/>
	<Tool
		Name="VCXDCMakeTool"
	/>
	<Tool
		Name="VCBscMakeTool"
	/>
	<Tool
		Name="VCFxCopTool"
	/>
	<Tool
		Name="VCPostBuildEventTool"
	/>
</Configuration>
