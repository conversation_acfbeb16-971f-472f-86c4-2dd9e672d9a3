cmake_minimum_required(VERSION 3.15.0)

project(PocoDataSQLiteExternal VERSION 1.14.2)

# 设置C++17标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# SQLCipher路径
set(SQLCIPHER_INCLUDE_DIR "D:/BWorkspace/TestFindGame/Thirdparty/Sqlcipher/Include")

# 根据POCO_BUILD_TYPE选择SQLCipher库路径
if(POCO_BUILD_TYPE STREQUAL "SHARED")
    # 动态库版本：使用Debug版本的SQLCipher
    set(SQLCIPHER_LIB_DIR "D:/BWorkspace/TestFindGame/Thirdparty/Sqlcipher/Debug/lib")
else()
    # 静态库版本：使用Release版本的SQLCipher
    set(SQLCIPHER_LIB_DIR "D:/BWorkspace/TestFindGame/Thirdparty/Sqlcipher/Release")
endif()

# 查找SQLCipher库
find_library(SQLCIPHER_LIBRARY
    NAMES sqlite3 sqlcipher
    PATHS ${SQLCIPHER_LIB_DIR}
    NO_DEFAULT_PATH
)

if(NOT SQLCIPHER_LIBRARY)
    message(FATAL_ERROR "SQLCipher library not found")
endif()

message(STATUS "SQLCipher library: ${SQLCIPHER_LIBRARY}")

# 源文件列表
set(SOURCES
    src/Binder.cpp
    src/Connector.cpp
    src/Extractor.cpp
    src/Notifier.cpp
    src/SessionImpl.cpp
    src/SQLiteException.cpp
    src/SQLiteStatementImpl.cpp
    src/Utility.cpp
)

# 根据POCO_BUILD_TYPE变量决定库类型
if(NOT DEFINED POCO_BUILD_TYPE)
    set(POCO_BUILD_TYPE "SHARED" CACHE STRING "Library type: SHARED or STATIC")
endif()

if(POCO_BUILD_TYPE STREQUAL "STATIC")
    add_library(PocoDataSQLiteExternal STATIC ${SOURCES})
    message(STATUS "Building static library")
else()
    add_library(PocoDataSQLiteExternal SHARED ${SOURCES})
    message(STATUS "Building shared library")
endif()

# 设置目标属性
set_target_properties(PocoDataSQLiteExternal PROPERTIES
    OUTPUT_NAME "PocoDataSQLiteExternal"
    VERSION ${PROJECT_VERSION}
)

# 包含目录
target_include_directories(PocoDataSQLiteExternal PRIVATE
    include
    ../../Foundation/include
    ../../Data/include
    ${SQLCIPHER_INCLUDE_DIR}
)

# 编译定义
target_compile_definitions(PocoDataSQLiteExternal PRIVATE
    WIN32
    _WINDOWS
    POCO_UNBUNDLED
#    SQLITE_THREADSAFE=1
#    SQLITE_ENABLE_FTS5
#    SQLITE_HAS_CODEC
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

# 根据配置添加特定定义
target_compile_definitions(PocoDataSQLiteExternal PRIVATE
    # Debug版本：动态库定义
#    $<$<CONFIG:Debug>:_USRDLL>
     _USRDLL
#    $<$<CONFIG:Debug>:SQLite_EXPORTS>
    SQLite_EXPORTS
#    $<$<CONFIG:Debug>:POCO_DLL>
    # Release版本：静态库定义
#    $<$<CONFIG:Release>:POCO_STATIC>
)

# Windows特定设置
if(WIN32)
    target_compile_definitions(PocoDataSQLiteExternal PRIVATE
        _CRT_SECURE_NO_WARNINGS
        _SCL_SECURE_NO_WARNINGS
    )

    target_compile_options(PocoDataSQLiteExternal PRIVATE
        /wd4996 /wd4244 /wd4018
    )
endif()

# Poco库路径
set(POCO_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../lib64")

# 根据POCO_BUILD_TYPE查找Poco库
if(POCO_BUILD_TYPE STREQUAL "SHARED")
    # 动态库版本：查找动态库导入库
    find_library(POCO_FOUNDATION_LIBRARY
        NAMES PocoFoundationd
        PATHS ${POCO_LIB_DIR}
        NO_DEFAULT_PATH
    )

    find_library(POCO_DATA_LIBRARY
        NAMES PocoDatad
        PATHS ${POCO_LIB_DIR}
        NO_DEFAULT_PATH
    )
else()
    # 静态库版本：查找静态库
    find_library(POCO_FOUNDATION_LIBRARY
        NAMES PocoFoundationmd
        PATHS ${POCO_LIB_DIR}
        NO_DEFAULT_PATH
    )

    find_library(POCO_DATA_LIBRARY
        NAMES PocoDatamd
        PATHS ${POCO_LIB_DIR}
        NO_DEFAULT_PATH
    )
endif()

if(NOT POCO_FOUNDATION_LIBRARY)
    message(FATAL_ERROR "Poco Foundation library not found in ${POCO_LIB_DIR}")
endif()

if(NOT POCO_DATA_LIBRARY)
    message(FATAL_ERROR "Poco Data library not found in ${POCO_LIB_DIR}")
endif()

message(STATUS "Poco Foundation library: ${POCO_FOUNDATION_LIBRARY}")
message(STATUS "Poco Data library: ${POCO_DATA_LIBRARY}")

# 链接库
if(POCO_BUILD_TYPE STREQUAL "SHARED")
    # 动态库：使用PRIVATE链接
    target_link_libraries(PocoDataSQLiteExternal PRIVATE
        ${POCO_FOUNDATION_LIBRARY}
        ${POCO_DATA_LIBRARY}
        ${SQLCIPHER_LIBRARY}
    )
else()
    # 静态库：使用PRIVATE链接，避免将依赖信息嵌入静态库
    # 使用者需要手动链接这些依赖库
    target_link_libraries(PocoDataSQLiteExternal PRIVATE
        ${POCO_FOUNDATION_LIBRARY}
        ${POCO_DATA_LIBRARY}
        ${SQLCIPHER_LIBRARY}
    )

    # 为静态库添加链接器选项，移除默认的库依赖
    if(MSVC)
        target_link_options(PocoDataSQLiteExternal PRIVATE
            "/NODEFAULTLIB:PocoFoundation.lib"
            "/NODEFAULTLIB:PocoData.lib"
        )
    endif()
endif()

# Windows系统库
if(WIN32)
    target_link_libraries(PocoDataSQLiteExternal PRIVATE
        ws2_32 iphlpapi crypt32
    )
endif()

# 安装配置（可选）
# install(TARGETS PocoDataSQLiteExternal
#     LIBRARY DESTINATION lib
#     ARCHIVE DESTINATION lib
#     RUNTIME DESTINATION bin
# )

message(STATUS "Configuration complete")
message(STATUS "SQLCipher: ${SQLCIPHER_LIBRARY}")
message(STATUS "Output: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")