静态库链接要求：
=================

当在您的项目中使用 PocoDataSQLiteExternal.lib 静态库时，您需要：

1. 确保您的项目也链接以下Poco库：
   - PocoFoundationmd.lib
   - PocoDatamd.lib

2. 在您的CMakeLists.txt中添加：
   target_link_libraries(your_target PRIVATE
       PocoDataSQLiteExternal.lib
       PocoFoundationmd.lib
       PocoDatamd.lib
       ws2_32 iphlpapi crypt32
   )

3. 或者在Visual Studio项目设置中添加这些库到附加依赖项

4. 确保定义 POCO_STATIC 宏：
   target_compile_definitions(your_target PRIVATE POCO_STATIC)

库文件位置：D:/CWorkspace/poco-poco-1.14.2-release/Data/SQLite/../../lib64
