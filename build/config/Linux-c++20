#
# Linux
#
# Make settings for Linux/gcc
#
#

#
# General Settings
#
LINKMODE ?= SHARED

SANITIZEFLAGS ?=

#
# Define Tools
#
CC      = ${CROSS_COMPILE}gcc
CXX     = ${CROSS_COMPILE}g++
LINK    = $(CXX)
LIB     = ${CROSS_COMPILE}ar -cr
RANLIB  = ${CROSS_COMPILE}ranlib
SHLIB   = $(CXX) -shared -Wl,-soname,$(notdir $@) -o $@
SHLIBLN = $(POCO_BASE)/build/script/shlibln
STRIP   = ${CROSS_COMPILE}strip
DEP     = $(POCO_BASE)/build/script/makedepend.gcc
SHELL   = sh
RM      = rm -rf
CP      = cp
MKDIR   = mkdir -p

#
# Extension for Shared Libraries
#
SHAREDLIBEXT     = .so.$(target_version)
SHAREDLIBLINKEXT = .so

#
# Compiler and Linker Flags
#
CFLAGS          = $(SANITIZEFLAGS) -std=c11
CFLAGS32        =
CFLAGS64        =
CXXFLAGS        = $(SANITIZEFLAGS) -std=c++20 -Wall -Wno-sign-compare
CXXFLAGS32      =
CXXFLAGS64      =
LINKFLAGS       = $(SANITIZEFLAGS)
LINKFLAGS32     =
LINKFLAGS64     =
STATICOPT_CC    =
STATICOPT_CXX   =
STATICOPT_LINK  = -static
SHAREDOPT_CC    = -fPIC
SHAREDOPT_CXX   = -fPIC
SHAREDOPT_LINK  = -Wl,-rpath,$(LIBPATH)
DEBUGOPT_CC     = -g -D_DEBUG
DEBUGOPT_CXX    = -g -D_DEBUG
DEBUGOPT_LINK   = -g
RELEASEOPT_CC   = -O2 -DNDEBUG
RELEASEOPT_CXX  = -O2 -DNDEBUG
RELEASEOPT_LINK = -O2

#
# System Specific Flags
#
SYSFLAGS = -D_XOPEN_SOURCE=600 -D_REENTRANT -D_THREAD_SAFE -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -DPOCO_HAVE_FD_EPOLL

#
# System Specific Libraries
#
SYSLIBS  = -lpthread -ldl -lrt
